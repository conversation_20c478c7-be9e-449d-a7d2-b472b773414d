{"name": "novafuse-competitive-baseline-engine", "version": "1.0.0", "description": "Competitive baseline simulation engine for rigorous NovaFuse benchmarking", "main": "competitive-baseline-engine.js", "scripts": {"start": "node competitive-baseline-engine.js", "test": "jest", "benchmark": "node scripts/run-benchmark.js", "validate": "node scripts/validate-baselines.js"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["novafuse", "cybersecurity", "benchmarking", "competitive-analysis", "mitre-attack", "performance-testing"], "author": "NovaFuse Technologies CTO Office", "license": "PROPRIETARY"}