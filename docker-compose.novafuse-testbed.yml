version: '3.8'

services:
  # NovaFuse Core Engine - Our Implementation
  novafuse-testbed:
    build:
      context: .
      dockerfile: Dockerfile.novafuse-testbed
    container_name: novafuse-comparison-engine
    ports:
      - "3000:3000"
      - "8080:8080"
    environment:
      - NODE_ENV=testing
      - COMPARISON_MODE=enabled
      - BENCHMARK_ITERATIONS=10000
      - OUTPUT_FORMAT=json
      - NIST_COMPLIANCE=enabled
      - DOCKER_TESTBED=true
      - CONSCIOUSNESS_THRESHOLD=2847
      - PI_COHERENCE_ENABLED=true
      - CASTL_VALIDATION=enabled
      - RIGOROUS_TESTING=true
      - STATISTICAL_VALIDATION=true
      - COMPETITIVE_BENCHMARKING=true
    volumes:
      - ./comparison-results:/app/results
      - ./test-data:/app/test-data
      - ./existing-results:/app/existing-results
      - ./rigorous-test-results:/app/rigorous-results
    networks:
      - novafuse-network
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - mongodb
      - competitive-baseline
      - statistical-validator

  # Competitive Baseline Simulator
  competitive-baseline:
    build:
      context: ./testing/competitive-simulators
      dockerfile: Dockerfile.competitive-baseline
    container_name: competitive-baseline-engine
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=testing
      - SIMULATION_MODE=crowdstrike_sentinelone_defender
      - BASELINE_ITERATIONS=10000
      - MITRE_ATTACK_DATASET=enabled
      - STATISTICAL_RIGOR=true
    volumes:
      - ./competitive-baselines:/app/baselines
      - ./mitre-attack-data:/app/mitre-data
      - ./comparison-results:/app/results
    networks:
      - novafuse-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Statistical Validation Service
  statistical-validator:
    build:
      context: ./testing/statistical-validation
      dockerfile: Dockerfile.statistical-validator
    container_name: statistical-validation-engine
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=testing
      - CONFIDENCE_LEVEL=95
      - SIGNIFICANCE_THRESHOLD=0.05
      - MIN_SAMPLE_SIZE=1000
      - OUTLIER_DETECTION=enabled
      - REGRESSION_ANALYSIS=enabled
    volumes:
      - ./comparison-results:/app/input-data
      - ./statistical-reports:/app/reports
      - ./validation-results:/app/validation
    networks:
      - novafuse-network
    depends_on:
      - redis

  # Performance Benchmark Orchestrator
  benchmark-orchestrator:
    build:
      context: ./testing/benchmark-orchestrator
      dockerfile: Dockerfile.benchmark-orchestrator
    container_name: benchmark-orchestrator
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=testing
      - ORCHESTRATION_MODE=comprehensive
      - TEST_SCENARIOS=mitre_attack,real_world,stress_test
      - PARALLEL_EXECUTION=true
      - RESULT_AGGREGATION=enabled
    volumes:
      - ./test-scenarios:/app/scenarios
      - ./orchestration-results:/app/results
      - ./test-reports:/app/reports
    networks:
      - novafuse-network
    depends_on:
      - novafuse-testbed
      - competitive-baseline
      - statistical-validator

  # Real-World Scenario Simulator
  real-world-simulator:
    build:
      context: ./testing/real-world-scenarios
      dockerfile: Dockerfile.real-world-simulator
    container_name: real-world-scenario-engine
    ports:
      - "3004:3000"
    environment:
      - NODE_ENV=testing
      - SCENARIO_TYPES=enterprise,healthcare,financial,government
      - DATA_VOLUME=high
      - ATTACK_SIMULATION=enabled
      - COMPLIANCE_VALIDATION=sox_hipaa_gdpr
    volumes:
      - ./real-world-data:/app/data
      - ./scenario-results:/app/results
      - ./compliance-reports:/app/compliance
    networks:
      - novafuse-network

  # Independent Validation Engine
  independent-validator:
    build:
      context: ./testing/independent-validation
      dockerfile: Dockerfile.independent-validator
    container_name: independent-validation-engine
    ports:
      - "3005:3000"
    environment:
      - NODE_ENV=testing
      - VALIDATION_MODE=third_party_simulation
      - NSS_LABS_SIMULATION=enabled
      - MITRE_EVALUATION=enabled
      - GARTNER_CRITERIA=enabled
      - BIAS_ELIMINATION=strict
    volumes:
      - ./independent-results:/app/results
      - ./third-party-reports:/app/reports
      - ./certification-data:/app/certification
    networks:
      - novafuse-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: novafuse-redis
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
    networks:
      - novafuse-network

  # MongoDB Database
  mongodb:
    image: mongo:6
    container_name: novafuse-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=novafuse
      - MONGO_INITDB_ROOT_PASSWORD=consciousness2847
    volumes:
      - mongodb_data:/data/db
      - ./test-data/mongodb-init:/docker-entrypoint-initdb.d
    networks:
      - novafuse-network

  # InfluxDB for Time-Series Performance Data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: novafuse-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=novafuse
      - DOCKER_INFLUXDB_INIT_PASSWORD=consciousness2847
      - DOCKER_INFLUXDB_INIT_ORG=novafuse-testing
      - DOCKER_INFLUXDB_INIT_BUCKET=performance-metrics
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - novafuse-network

  # Grafana for Real-Time Monitoring
  grafana:
    image: grafana/grafana:10.2.0
    container_name: novafuse-grafana
    ports:
      - "3006:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=consciousness2847
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./testing/grafana-dashboards:/etc/grafana/provisioning/dashboards
      - ./testing/grafana-datasources:/etc/grafana/provisioning/datasources
    networks:
      - novafuse-network
    depends_on:
      - influxdb

networks:
  novafuse-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mongodb_data:
  influxdb_data:
  grafana_data:
